@page "/home"
@model EdTech.Study.Web.Pages.Guest.HomeModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <title>EdTech Platform</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link href="~/css/home/<USER>" rel="stylesheet" />
    <link href="~/libs/bootstrap/css/bootstrap.css" rel="stylesheet" />

</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <img class="logo-img" alt="Mask group" src="/mask-group-4.png" />
                <div class="logo-text">EDTECH</div>
            </div>

            <nav class="nav-menu">
                <a href="#" class="nav-item active">
                    <img src="/icon-20.svg" alt="Home" />
                    <span>TRANG CHỦ</span>
                </a>
                <a href="#" class="nav-item">
                    <img src="/icon-40.svg" alt="Lessons" />
                    <span>BÀI GIẢNG</span>
                </a>
                <a href="#" class="nav-item">
                    <img src="/icon-8.svg" alt="Exercises" />
                    <span>BÀI TẬP</span>
                </a>
                <a href="#" class="nav-item">
                    <img src="/icon.svg" alt="Articles" />
                    <span>BÀI VIẾT</span>
                </a>
                <a href="#" class="nav-item">
                    <img src="/icon-3.svg" alt="Contact" />
                    <span>LIÊN HỆ</span>
                </a>
            </nav>

            <div class="auth-section">
                <img class="auth-img" alt="User" src="/image-21.png" />
                <a href="#" class="auth-link">Đăng nhập</a>
                <div class="separator"></div>
                <a href="#" class="auth-link">Đăng ký</a>
            </div>
        </div>
    </header>

    <main>
        <section class="hero">
            <h1>Hệ thống giáo dục thông minh<br>Đa dạng bài giảng, mô phỏng sinh động, giảng dạy hiệu quả</h1>
            <p>Hệ thống mang đến kho bài giảng đa dạng, mô phỏng sinh động và công cụ hỗ trợ giảng dạy hiện đại, giúp học sinh tiếp thu kiến thức dễ dàng và giáo viên nâng cao hiệu quả giảng dạy.</p>
            <button class="cta-button">
                THỬ NGAY
                <img src="/icon-9.svg" alt="Arrow" />
            </button>
        </section>

        <section class="subjects">
            <h2>Đa Dạng Bài Giảng Của Tất Cả Môn Học</h2>
            <div class="separator-line"></div>
            <p>Chúng tôi cung cấp kho bài giảng phong phú, đa dạng với đầy đủ tất cả các môn học, giúp dễ dàng tiếp cận kiến thức, nâng cao hiệu quả học tập và chinh phục mọi mục tiêu.</p>

            <div class="subjects-grid">
                <div class="subjects-row">
                    <div class="subject-card math">
                        <img src="/image-2.png" alt="Toán Học" />
                        <h4>Toán Học</h4>
                        <div class="arrow-button"></div>
                    </div>
                    <div class="subject-card literature">
                        <img src="/image-3.png" alt="Ngữ Văn" />
                        <h4>Ngữ Văn</h4>
                        <div class="arrow-button"></div>
                    </div>
                    <div class="subject-card language">
                        <img src="/image-4.png" alt="Ngoại Ngữ" />
                        <h4>Ngoại Ngữ</h4>
                        <div class="arrow-button"></div>
                    </div>
                </div>
                <div class="subjects-row">
                    <div class="subject-card science">
                        <img src="/image-5.png" alt="Khoa Học Tự Nhiên" />
                        <h4>Khoa Học Tự Nhiên</h4>
                        <div class="arrow-button"></div>
                    </div>
                    <div class="subject-card social">
                        <img src="/image-6.png" alt="Khoa Học Xã Hội" />
                        <h4>Khoa Học Xã Hội</h4>
                        <div class="arrow-button"></div>
                    </div>
                </div>
            </div>
        </section>

        <section class="courses">
            <h2>Bài Giảng Dễ Hiểu</h2>
            <div class="separator-line"></div>
            <p>Hệ thống mô phỏng bài giảng được thiết kế sinh động, trực quan với giao diện thân thiện, giúp người học dễ dàng tiếp cận và hiểu sâu kiến thức một cách nhanh chóng, hiệu quả, biến việc học trở nên thú vị và dễ dàng hơn bao giờ hết.</p>

            <div class="courses-grid">
                <div class="course-card">
                    <img src="/image-12.png" alt="Course thumbnail" class="course-image" />
                    <div class="course-tags">
                        <span class="tag subject-tag">
                            <img src="/icon-7.svg" alt="Subject icon" />
                            KHTN
                        </span>
                        <span class="tag grade-tag">LỚP 6</span>
                    </div>
                    <h3>Bài 4: Sơ lược về bảng tuần hoàn các nguyên tố hóa học</h3>
                    <div class="course-stats">
                        <div class="stat">
                            <img src="/icon-1.svg" alt="Game icon" />
                            <span>1 trò chơi</span>
                        </div>
                        <div class="stat">
                            <img src="/icon-4.svg" alt="Exercise icon" />
                            <span>3 bài tập</span>
                        </div>
                    </div>
                    <button class="course-button">
                        <span>Vào học</span>
                        <img src="/icon-9.svg" alt="Arrow icon" />
                    </button>
                </div>
                <!-- Repeat course-card div 3 more times with different content -->
            </div>
        </section>

        <section class="statistics">
            <div class="container">
                <div class="stat-item">
                    <div class="stat-icon">
                        <img src="/image-8-1.png" alt="Students" />
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">1500+</div>
                        <div class="stat-label">Học Sinh</div>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon">
                        <img src="/image-14.png" alt="Teachers" />
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">150+</div>
                        <div class="stat-label">Giáo Viên</div>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon">
                        <img src="/image-15.png" alt="Lessons" />
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">500+</div>
                        <div class="stat-label">Bài Giảng</div>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon">
                        <img src="/image-16.png" alt="Exercises" />
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">1000+</div>
                        <div class="stat-label">Bài Luyện Tập</div>
                    </div>
                </div>
            </div>
        </section>

        <section class="exams">
            <h2>Bài tập</h2>
            <div class="separator-line"></div>
            <div class="p-4 bg-light">
                <div class="container-xl mx-auto">
                    <div class="row g-4">
                        <div class="col-12 col-md-6 col-lg-3">
                            <div class="bg-white rounded p-4 shadow-sm border border-light d-flex flex-column h-100">
                                <div class="d-flex align-items-center gap-2 mb-3">
                                    <div class="px-2 py-1 rounded small d-flex align-items-center gap-1 bg-success bg-opacity-10 text-success">
                                        <img src="/icon-7.svg" alt="Subject icon" width="16" height="16" />
                                        <span>KHTN</span>
                                    </div>
                                    <div class="small text-muted">Lớp 6</div>
                                </div>
                                <h5 class="fw-medium text-dark mb-3">Bài tập về nguyên tố hóa học</h5>
                                <div class="d-flex align-items-center gap-2 small text-muted mb-3">
                                    <span class="d-flex align-items-center gap-1">
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                            <path d="M8 1v14M1 8h14" stroke="currentColor" stroke-width="2" />
                                        </svg>
                                        10 câu
                                    </span>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between small mb-2">
                                        <span class="text-muted">Tiến độ: 0%</span>
                                        <span class="text-danger">Chưa làm</span>
                                    </div>
                                    <div class="progress" style="height: 4px;">
                                        <div class="progress-bar bg-danger" role="progressbar" style="width: 0%"></div>
                                    </div>
                                </div>
                                <button class="btn btn-danger w-100 mt-auto">
                                    Làm bài
                                </button>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 col-lg-3">
                            <div class="bg-white rounded p-4 shadow-sm border border-light d-flex flex-column h-100">
                                <div class="d-flex align-items-center gap-2 mb-3">
                                    <div class="px-2 py-1 rounded small d-flex align-items-center gap-1 bg-danger bg-opacity-10 text-danger">
                                        <img src="/icon-7.svg" alt="Subject icon" width="16" height="16" />
                                        <span>Toán</span>
                                    </div>
                                    <div class="small text-muted">Lớp 7</div>
                                </div>
                                <h5 class="fw-medium text-dark mb-3">Bài tập về phân số</h5>
                                <div class="d-flex align-items-center gap-2 small text-muted mb-3">
                                    <span class="d-flex align-items-center gap-1">
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                            <path d="M8 1v14M1 8h14" stroke="currentColor" stroke-width="2" />
                                        </svg>
                                        15 câu
                                    </span>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between small mb-2">
                                        <span class="text-muted">Tiến độ: 30%</span>
                                        <span class="text-warning">Đang làm</span>
                                    </div>
                                    <div class="progress" style="height: 4px;">
                                        <div class="progress-bar bg-danger" role="progressbar" style="width: 30%"></div>
                                    </div>
                                </div>
                                <button class="btn btn-danger w-100 mt-auto">
                                    Làm bài
                                </button>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 col-lg-3">
                            <div class="bg-white rounded p-4 shadow-sm border border-light d-flex flex-column h-100">
                                <div class="d-flex align-items-center gap-2 mb-3">
                                    <div class="px-2 py-1 rounded small d-flex align-items-center gap-1 bg-primary bg-opacity-10 text-primary">
                                        <img src="/icon-7.svg" alt="Subject icon" width="16" height="16" />
                                        <span>Văn</span>
                                    </div>
                                    <div class="small text-muted">Lớp 8</div>
                                </div>
                                <h5 class="fw-medium text-dark mb-3">Bài tập văn học</h5>
                                <div class="d-flex align-items-center gap-2 small text-muted mb-3">
                                    <span class="d-flex align-items-center gap-1">
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                            <path d="M8 1v14M1 8h14" stroke="currentColor" stroke-width="2" />
                                        </svg>
                                        8 câu
                                    </span>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between small mb-2">
                                        <span class="text-muted">Tiến độ: 100%</span>
                                        <span class="text-success">Hoàn thành</span>
                                    </div>
                                    <div class="progress" style="height: 4px;">
                                        <div class="progress-bar bg-success" role="progressbar" style="width: 100%"></div>
                                    </div>
                                </div>
                                <button class="btn btn-danger w-100 mt-auto">
                                    Xem lại
                                </button>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 col-lg-3">
                            <div class="bg-white rounded p-4 shadow-sm border border-light d-flex flex-column h-100">
                                <div class="d-flex align-items-center gap-2 mb-3">
                                    <div class="px-2 py-1 rounded small d-flex align-items-center gap-1 bg-warning bg-opacity-10 text-warning">
                                        <img src="/icon-7.svg" alt="Subject icon" width="16" height="16" />
                                        <span>Anh</span>
                                    </div>
                                    <div class="small text-muted">Lớp 9</div>
                                </div>
                                <h5 class="fw-medium text-dark mb-3">Bài tập ngữ pháp</h5>
                                <div class="d-flex align-items-center gap-2 small text-muted mb-3">
                                    <span class="d-flex align-items-center gap-1">
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                            <path d="M8 1v14M1 8h14" stroke="currentColor" stroke-width="2" />
                                        </svg>
                                        12 câu
                                    </span>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between small mb-2">
                                        <span class="text-muted">Tiến độ: 60%</span>
                                        <span class="text-warning">Đang làm</span>
                                    </div>
                                    <div class="progress" style="height: 4px;">
                                        <div class="progress-bar bg-warning" role="progressbar" style="width: 60%"></div>
                                    </div>
                                </div>
                                <button class="btn btn-danger w-100 mt-auto">
                                    Tiếp tục
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Custom Testimonial Carousel Section -->
        <section class="testimonial">
            <h2>Công cụ hỗ trợ được giáo viên tin dùng</h2>
            <div class="separator-line"></div>
            <p>Học sinh chia sẻ cảm nhận về hệ thống giáo dục thông minh.</p>

            <div class="testimonial-carousel-container">
                <div class="testimonial-carousel" id="testimonialCarousel">
                    <!-- Testimonial Item 1 -->
                    <div class="testimonial-item">
                        <div class="testimonial-card">
                            <img src="/image-testimonial-1.png" alt="Học sinh 1" class="testimonial-avatar" />
                            <div class="testimonial-name">Nguyễn Minh An</div>
                            <div class="testimonial-text">
                                "Hệ thống học tập này thật sự tuyệt vời! Các bài giảng rất sinh động và dễ hiểu."
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial Item 2 -->
                    <div class="testimonial-item">
                        <div class="testimonial-card">
                            <img src="/image-testimonial-2.png" alt="Giáo viên 1" class="testimonial-avatar" />
                            <div class="testimonial-name">Cô Phạm Thu Hà</div>
                            <div class="testimonial-text">
                                "Công cụ hỗ trợ giảng dạy rất hữu ích, giúp tôi tạo ra những bài học thú vị."
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial Item 3 -->
                    <div class="testimonial-item">
                        <div class="testimonial-card">
                            <img src="/image-testimonial-3.png" alt="Phụ huynh 1" class="testimonial-avatar" />
                            <div class="testimonial-name">Chị Trần Thị Lan</div>
                            <div class="testimonial-text">
                                "Con tôi rất thích học trên hệ thống này. Các bài tập đa dạng và phù hợp."
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial Item 4 -->
                    <div class="testimonial-item">
                        <div class="testimonial-card">
                            <img src="/image-testimonial-1.png" alt="Học sinh 2" class="testimonial-avatar" />
                            <div class="testimonial-name">Trần Văn Hưng</div>
                            <div class="testimonial-text">
                                "Giao diện thân thiện và các bài tập được phân loại rõ ràng theo từng cấp độ."
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial Item 5 -->
                    <div class="testimonial-item">
                        <div class="testimonial-card">
                            <img src="/image-testimonial-2.png" alt="Giáo viên 2" class="testimonial-avatar" />
                            <div class="testimonial-name">Thầy Nguyễn Đức</div>
                            <div class="testimonial-text">
                                "Hệ thống theo dõi tiến độ học tập giúp tôi nắm bắt tình hình học sinh."
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial Item 6 -->
                    <div class="testimonial-item">
                        <div class="testimonial-card">
                            <img src="/image-testimonial-3.png" alt="Phụ huynh 2" class="testimonial-avatar" />
                            <div class="testimonial-name">Chị Lê Thị Mai</div>
                            <div class="testimonial-text">
                                "Tôi yên tâm khi con học online với hệ thống này vì có nhiều tính năng hỗ trợ."
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Custom Arrow Buttons -->
                <button class="testimonial-arrow testimonial-arrow-left" id="prevBtn" aria-label="Previous testimonial">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                </button>
                <button class="testimonial-arrow testimonial-arrow-right" id="nextBtn" aria-label="Next testimonial">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                </button>
            </div>
        </section>
        <!-- Đánh giá của học sinh -->
        <section class="student-feedback">
            <!-- Header Section -->
            <h2>Đánh Giá Của Học Sinh</h2>
            <div class="separator-line"></div>
                <p>
                    Hệ thống giúp học sinh tiếp thu kiến thức dễ dàng hơn với bài giảng sinh động, mô phỏng trực quan và công cụ hỗ trợ học tập hiệu quả,
                    biến việc học trở nên thú vị và chủ động hơn.
                </p>

            <!-- Testimonials Section -->
            <div class="testimonials py-3">
                <div id="testimonialCarousel" class="carousel slide" data-bs-ride="false">
                    <div class="carousel-inner">
                        <!-- Testimonial 1 -->
                        <div class="carousel-item active">
                            <div class="row justify-content-center">
                                <div class="col-md-10 col-lg-8">
                                    <div class="testimonial-card">
                                        <p class="testimonial-text">
                                            Từ khi sử dụng hệ thống này, em cảm thấy việc học trở nên dễ dàng và thú vị hơn rất nhiều. Các bài giảng không chỉ đầy đủ mà còn được trình bày một cách sinh động, với hình ảnh, video và mô phỏng trực quan giúp em hiểu bài nhanh hơn.
                                        </p>
                                        <div class="student-info">
                                            <img src="https://images.pexels.com/photos/762020/pexels-photo-762020.jpeg?auto=compress&cs=tinysrgb&w=150" class="student-image" alt="Học sinh">
                                            <div class="student-details">
                                                <h4 class="student-name">Julian</h4>
                                                <p class="student-class">Lớp 8</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Testimonial 2 -->
                        <div class="carousel-item">
                            <div class="row justify-content-center">
                                <div class="col-md-10 col-lg-8">
                                    <div class="testimonial-card">
                                        <p class="testimonial-text">
                                            Hệ thống này thực sự giúp em thay đổi cách học một cách tích cực. Hệ thống có các bài tập luyện tập và kiểm tra ngay sau mỗi bài học, giúp em củng cố kiến thức kịp thời. Nhờ đó, em tự tin hơn trong học tập và đạt kết quả tốt.
                                        </p>
                                        <div class="student-info">
                                            <img src="https://images.pexels.com/photos/762020/pexels-photo-762020.jpeg?auto=compress&cs=tinysrgb&w=150" class="student-image" alt="Học sinh">
                                            <div class="student-details">
                                                <h4 class="student-name">Julian</h4>
                                                <p class="student-class">Lớp 10</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Testimonial 3 -->
                        <div class="carousel-item">
                            <div class="row justify-content-center">
                                <div class="col-md-10 col-lg-8">
                                    <div class="testimonial-card">
                                        <p class="testimonial-text">
                                            Từ khi sử dụng hệ thống này, em cảm thấy việc học trở nên dễ dàng và thú vị hơn rất nhiều. Các bài giảng không chỉ đầy đủ mà còn được trình bày một cách sinh động, với hình ảnh, video và mô phỏng trực quan giúp em hiểu bài nhanh hơn.
                                        </p>
                                        <div class="student-info">
                                            <img src="https://images.pexels.com/photos/762020/pexels-photo-762020.jpeg?auto=compress&cs=tinysrgb&w=150" class="student-image" alt="Học sinh">
                                            <div class="student-details">
                                                <h4 class="student-name">Julian</h4>
                                                <p class="student-class">Lớp 12</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Testimonial 4 -->
                        <div class="carousel-item">
                            <div class="row justify-content-center">
                                <div class="col-md-10 col-lg-8">
                                    <div class="testimonial-card">
                                        <p class="testimonial-text">
                                            Từ khi sử dụng hệ thống này, em cảm thấy việc học trở nên dễ dàng và thú vị hơn rất nhiều. Các bài giảng không chỉ đầy đủ mà còn được trình bày một cách sinh động, với hình ảnh, video và mô phỏng trực quan giúp em hiểu bài nhanh hơn.
                                        </p>
                                        <div class="student-info">
                                            <img src="https://images.pexels.com/photos/762020/pexels-photo-762020.jpeg?auto=compress&cs=tinysrgb&w=150" class="student-image" alt="Học sinh">
                                            <div class="student-details">
                                                <h4 class="student-name">Julian</h4>
                                                <p class="student-class">Lớp 12</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="carousel-item">
                            <div class="row justify-content-center">
                                <div class="col-md-10 col-lg-8">
                                    <div class="testimonial-card">
                                        <p class="testimonial-text">
                                            Từ khi sử dụng hệ thống này, em cảm thấy việc học trở nên dễ dàng và thú vị hơn rất nhiều. Các bài giảng không chỉ đầy đủ mà còn được trình bày một cách sinh động, với hình ảnh, video và mô phỏng trực quan giúp em hiểu bài nhanh hơn.
                                        </p>
                                        <div class="student-info">
                                            <img src="https://images.pexels.com/photos/762020/pexels-photo-762020.jpeg?auto=compress&cs=tinysrgb&w=150" class="student-image" alt="Học sinh">
                                            <div class="student-details">
                                                <h4 class="student-name">Julian</h4>
                                                <p class="student-class">Lớp 12</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="carousel-item">
                            <div class="row justify-content-center">
                                <div class="col-md-10 col-lg-8">
                                    <div class="testimonial-card">
                                        <p class="testimonial-text">
                                            Từ khi sử dụng hệ thống này, em cảm thấy việc học trở nên dễ dàng và thú vị hơn rất nhiều. Các bài giảng không chỉ đầy đủ mà còn được trình bày một cách sinh động, với hình ảnh, video và mô phỏng trực quan giúp em hiểu bài nhanh hơn.
                                        </p>
                                        <div class="student-info">
                                            <img src="https://images.pexels.com/photos/762020/pexels-photo-762020.jpeg?auto=compress&cs=tinysrgb&w=150" class="student-image" alt="Học sinh">
                                            <div class="student-details">
                                                <h4 class="student-name">Julian</h4>
                                                <p class="student-class">Lớp 12</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Navigation Buttons -->
                    <button class="carousel-control-prev" type="button" data-bs-target="#testimonialCarousel" data-bs-slide="prev">
                        <span class="carousel-arrow" aria-hidden="true">
                            <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M15 18l-6-6 6-6" />
                            </svg>
                        </span>
                        <span class="visually-hidden">Previous</span>
                    </button>
                    <button class="carousel-control-next" type="button" data-bs-target="#testimonialCarousel" data-bs-slide="next">
                        <span class="carousel-arrow" aria-hidden="true">
                            <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M9 18l6-6-6-6" />
                            </svg>
                        </span>
                        <span class="visually-hidden">Next</span>
                    </button>
                </div>
            </div>
        </section>
        <section class="articles">
            <h2>Bài Viết Mới</h2>
            <div class="separator-line"></div>
            <p>Tổng hợp các bài viết chia sẻ thông tin và kinh nghiệm học tập từ các giáo viên và học sinh.</p>

            <div class="articles-grid">
                <article class="article-card">
                    <img src="/image-3-4.png" alt="Article thumbnail" />
                    <h3>Khoá học mùa hè bắt đầu từ 01/06</h3>
                    <p>Morbi accumsan ipsum velit. Nam nec tellus a odio tincidunt auctor a ornare odio. Sed non mauris itae erat conuat.</p>
                </article>
                <article class="article-card">
                    <img src="/image-3-4.png" alt="Article thumbnail" />
                    <h3>Khoá học mùa hè bắt đầu từ 01/06</h3>
                    <p>Morbi accumsan ipsum velit. Nam nec tellus a odio tincidunt auctor a ornare odio. Sed non mauris itae erat conuat.</p>
                </article>
                <article class="article-card">
                    <img src="/image-3-4.png" alt="Article thumbnail" />
                    <h3>Khoá học mùa hè bắt đầu từ 01/06</h3>
                    <p>Morbi accumsan ipsum velit. Nam nec tellus a odio tincidunt auctor a ornare odio. Sed non mauris itae erat conuat.</p>
                </article>
                <article class="article-card">
                    <img src="/image-3-4.png" alt="Article thumbnail" />
                    <h3>Khoá học mùa hè bắt đầu từ 01/06</h3>
                    <p>Morbi accumsan ipsum velit. Nam nec tellus a odio tincidunt auctor a ornare odio. Sed non mauris itae erat conuat.</p>
                </article>
            </div>
        </section>
    </main>

    <div class="social-widget">
        <img src="/image-22.png" alt="Social 1" />
        <img src="/image-23.png" alt="Social 2" />
        <img src="/image-24.png" alt="Social 3" />
    </div>
    <script src="~/libs/bootstrap/js/bootstrap.bundle.js"></script>
    <script src="~/js/home/<USER>"></script>
</body>
</html>